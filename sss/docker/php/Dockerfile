# Usa un'immagine ufficiale di PHP 8.1 FPM su Alpine Linux.
FROM php:8.1-fpm-alpine

# Argomenti per la versione di Drush
ARG DRUSH_VERSION=^11

# Variabili d'ambiente
ENV COMPOSER_ALLOW_SUPERUSER=1
ENV PATH="/var/www/html/vendor/bin:$PATH"

# Imposta la directory di lavoro
WORKDIR /var/www/html

# Installa le dipendenze di sistema necessarie per Drupal e le estensioni PHP
RUN apk add --no-cache \
        $PHPIZE_DEPS \
        bash \
        git \
        libzip-dev \
        libjpeg-turbo-dev \
        libpng-dev \
        freetype-dev \
        libintl \
        icu-dev \
        libxslt-dev \
        zip && \
    docker-php-ext-configure gd --with-freetype --with-jpeg && \
    docker-php-ext-install -j$(nproc) \
        gd \
        pdo_mysql \
        opcache \
        intl \
        bcmath \
        soap \
        zip \
        xsl && \
    apk del $PHPIZE_DEPS

# Installa Composer (gestore di pacchetti per PHP)
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Installa Drush (strumento a riga di comando per Drupal)
RUN composer global require drush/drush:${DRUSH_VERSION}

# NON creiamo più il progetto qui. Lo faremo dopo.
# Rimosse le righe RUN composer create-project e RUN chown
version: '3.8'

services:
  # Servizio PHP/Drupal
  drupal:
    build:
      context: ./docker/php
      args:
        DRUPAL_VERSION: ${DRUPAL_VERSION:-^9.5} # Usa la variabile da .env
    container_name: ${PROJECT_NAME}_drupal
    restart: unless-stopped
    volumes:
      # Binda il codice di Drupal per lo sviluppo in locale
      # Il contenuto di 'drupal' verrà popolato da Composer al primo avvio
      - ./drupal:/var/www/html
    networks:
      - drupal-network

  # Servizio Nginx (web server)
  nginx:
    image: nginx:1.21-alpine
    container_name: ${PROJECT_NAME}_nginx
    restart: unless-stopped
    ports:
      - "${DRUPAL_PORT}:80"
    volumes:
      - ./drupal:/var/www/html
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - drupal
    networks:
      - drupal-network

  # Servizio Database MySQL
  mysql:
    image: mysql:8.0
    platform: linux/arm64
    container_name: ${PROJECT_NAME}_mysql
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password
    environment:
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
    volumes:
      # Binda i dati di MySQL per non perderli al riavvio
      - mysql-data:/var/lib/mysql
    networks:
      - drupal-network

  # Servizio Adminer (per la gestione del DB)
  adminer:
    image: adminer
    platform: linux/arm64
    container_name: ${PROJECT_NAME}_adminer
    restart: unless-stopped
    ports:
      - "${ADMINER_PORT}:8080"
    depends_on:
      - mysql
    networks:
      - drupal-network

# Network per la comunicazione tra i container
networks:
  drupal-network:
    driver: bridge

# Volume per la persistenza dei dati di MySQL
volumes:
  mysql-data:
    driver: local
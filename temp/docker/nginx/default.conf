server {
    listen 80;
    server_name localhost;
    root /var/www/html/web; # La document root di Drupal è nella sottocartella 'web'

    location / {
        try_files $uri /index.php?$query_string;
    }

    location = /favicon.ico {
        log_not_found off;
        access_log off;
    }

    location = /robots.txt {
        allow all;
        log_not_found off;
        access_log off;
    }

    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass drupal:9000; # Comunica col servizio 'drupal' sulla porta FPM
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }

    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        try_files $uri @rewrite;
        expires max;
        log_not_found off;
    }

    # Impedisce l'accesso a file sensibili
    location ~ ^/sites/.*/private/ {
        return 403;
    }

    # Impedisce l'accesso a file di configurazione
    location ~ (^|/)\. {
        return 403;
    }
}
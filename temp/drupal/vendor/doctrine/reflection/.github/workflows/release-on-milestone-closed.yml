name: "Automatic Releases"

on:
  milestone:
    types:
      - "closed"

jobs:
  release:
    uses: "doctrine/.github/.github/workflows/release-on-milestone-closed.yml@1.4.1"
    secrets:
      GIT_AUTHOR_EMAIL: ${{ secrets.GIT_AUTHOR_EMAIL }}
      GIT_AUTHOR_NAME: ${{ secrets.GIT_AUTHOR_NAME }}
      ORGANIZATION_ADMIN_TOKEN: ${{ secrets.ORGANIZATION_ADMIN_TOKEN }}
      SIGNING_SECRET_KEY: ${{ secrets.SIGNING_SECRET_KEY }}

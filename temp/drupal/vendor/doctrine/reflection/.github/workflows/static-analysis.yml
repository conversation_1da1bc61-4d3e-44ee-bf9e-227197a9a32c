name: "Static Analysis"

on:
  pull_request:
    branches:
      - "*.x"
  push:
    branches:
      - "*.x"

jobs:
  phpstan:
    name: "PHPStan"
    runs-on: "ubuntu-20.04"
    env:
      COMPOSER_ROOT_VERSION: "1.2"

    strategy:
      matrix:
        php-version:
          - "8.1"

    steps:
      - name: "Checkout code"
        uses: "actions/checkout@v2"

      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          coverage: "none"
          php-version: "${{ matrix.php-version }}"

      - name: "Install dependencies with Composer"
        uses: "ramsey/composer-install@v1"
        with:
          dependency-versions: "highest"

      - name: "Run a static analysis with phpstan/phpstan"
        run: "vendor/bin/phpstan analyse"

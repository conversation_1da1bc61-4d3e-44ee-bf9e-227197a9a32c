{"name": "asm89/stack-cors", "description": "Cross-origin resource sharing library and stack middleware", "keywords": ["stack", "cors"], "homepage": "https://github.com/asm89/stack-cors", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=5.5.9", "symfony/http-foundation": "~2.7|~3.0|~4.0|~5.0", "symfony/http-kernel": "~2.7|~3.0|~4.0|~5.0"}, "require-dev": {"phpunit/phpunit": "^5.0 || ^4.8.10", "squizlabs/php_codesniffer": "^2.3"}, "autoload": {"psr-4": {"Asm89\\Stack\\": "src/Asm89/Stack/"}}, "autoload-dev": {"psr-4": {"Asm89\\Stack\\": "test/Asm89/Stack/"}}, "scripts": {"test": "phpunit", "check-style": "phpcs -p --standard=PSR2 --runtime-set ignore_errors_on_exit 1 --runtime-set ignore_warnings_on_exit 1 src", "fix-style": "phpcbf -p --standard=PSR2 --runtime-set ignore_errors_on_exit 1 --runtime-set ignore_warnings_on_exit 1 src"}, "extra": {"branch-alias": {"dev-master": "1.2-dev"}}}